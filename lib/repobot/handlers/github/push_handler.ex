defmodule Repobot.Handlers.GitHub.PushHandler do
  @moduledoc """
  Handles GitHub push events by processing commits and syncing changes to target repositories.
  """

  require Logger
  import Ecto.Query

  alias Repobot.{Events, Repositories, SourceFiles, RepositoryFiles}

  defmodule Push do
    @moduledoc """
    Represents a GitHub push event with processed attributes for easier handling.
    """
    defstruct [
      :repository_full_name,
      :repository_id,
      :organization_id,
      :ref,
      :default_branch,
      :commits,
      :template_repository
    ]

    @type commit :: %{
            id: String.t(),
            message: String.t(),
            modified_files: [String.t()],
            added_files: [String.t()]
          }

    @type t :: %__MODULE__{
            repository_full_name: String.t(),
            repository_id: integer() | nil,
            organization_id: integer() | nil,
            ref: String.t(),
            default_branch: String.t(),
            commits: [commit()],
            template_repository: Repobot.Repositories.Repository.t() | nil
          }

    @doc """
    Creates a new Push struct from a GitHub webhook payload.
    """
    def new(payload) do
      repository_full_name = payload["repository"]["full_name"]
      repository = Repositories.get_repository_by(full_name: repository_full_name)

      commits =
        Enum.map(payload["commits"] || [], fn commit ->
          %{
            id: commit["id"],
            message: commit["message"],
            modified_files: commit["modified"] || [],
            added_files: commit["added"] || []
          }
        end)

      %__MODULE__{
        repository_full_name: repository_full_name,
        repository_id: repository && repository.id,
        organization_id: repository && repository.organization_id,
        ref: payload["ref"],
        default_branch: payload["repository"]["default_branch"],
        commits: commits,
        template_repository: nil
      }
    end

    @doc """
    Returns true if the push is to the default branch.
    """
    def to_default_branch?(%__MODULE__{ref: ref, default_branch: default_branch}) do
      String.replace_prefix(ref, "refs/heads/", "") == default_branch
    end

    @doc """
    Loads the template repository if this push is to a template repository.
    Returns {:ok, push} if successful, {:error, reason} otherwise.
    """
    def load_template_repository(%__MODULE__{repository_full_name: full_name} = push) do
      case Repositories.get_template_repository_by(full_name: full_name) do
        {:ok, template_repo} ->
          {:ok, %{push | template_repository: template_repo}}

        {:error, :not_found} ->
          {:ok, push}
      end
    end
  end

  @doc """
  Handles a GitHub push event.
  """
  def handle(payload) do
    push = Push.new(payload)

    # Log the push event
    if push.organization_id do
      Events.log_github_event("push", payload, push.organization_id, nil, push.repository_id)
    end

    # Always refresh repository files when push is to default branch
    if Push.to_default_branch?(push) && push.repository_id do
      refresh_repository_files(push.repository_id, payload["commits"] || [])
    end

    with {:ok, push} <- Push.load_template_repository(push),
         true <- should_process_push?(push) do
      process_push(push)
    else
      false ->
        Logger.info("Skipping push: not to default branch or not a template repository")
        :ok

      {:error, reason} ->
        Logger.error("Error processing push: #{inspect(reason)}")
        {:error, reason}
    end
  end

  defp should_process_push?(push) do
    Push.to_default_branch?(push) && push.template_repository != nil
  end

  defp process_push(%Push{commits: commits, template_repository: template_repo}) do
    # Group files by commit to preserve commit structure
    commits
    |> Enum.reduce({%{}, []}, fn commit, {processed_files, results} ->
      modified_files = commit.modified_files ++ commit.added_files

      # Find source files that match the modified files
      # Use the new function that handles both template and non-template files
      source_files =
        SourceFiles.find_by_repository_and_file_paths(
          template_repo.id,
          modified_files,
          preload: [:repositories, :source_repository]
        )

      if Enum.empty?(source_files) do
        {processed_files, results}
      else
        # Create a mapping of source files to their corresponding repository file paths
        source_file_to_repo_path =
          create_source_file_to_repo_path_mapping(source_files, modified_files)

        process_commit(
          commit,
          source_files,
          template_repo,
          processed_files,
          results,
          source_file_to_repo_path
        )
      end
    end)
    |> elem(1)
    |> case do
      [] -> :ok
      results -> {:ok, results}
    end
  end

  # Creates a mapping from source files to their corresponding repository file paths
  defp create_source_file_to_repo_path_mapping(source_files, modified_files) do
    Enum.reduce(source_files, %{}, fn source_file, acc ->
      # For template files, the repository file path has .liquid extension
      # For non-template files, the repository file path matches target_path
      repo_path =
        if source_file.is_template do
          # Find the .liquid file that corresponds to this source file's target_path
          Enum.find(modified_files, fn path ->
            String.ends_with?(path, ".liquid") &&
              String.replace_suffix(path, ".liquid", "") == source_file.target_path
          end) || source_file.target_path
        else
          source_file.target_path
        end

      Map.put(acc, source_file.id, repo_path)
    end)
  end

  defp process_commit(
         commit,
         source_files,
         template_repo,
         processed_files,
         results,
         source_file_to_repo_path
       ) do
    github_api = Application.get_env(:repobot, :github_api)
    sync_backend = Application.get_env(:repobot, :sync_backend)

    # Create a GitHub client for the template repository
    template_client = github_api.client(template_repo.owner, template_repo.name)

    # Attempt to fetch all source file contents for this commit
    {final_processed_map, ok_files, errors} =
      fetch_source_file_contents(
        source_files,
        template_repo,
        template_client,
        commit,
        processed_files,
        source_file_to_repo_path
      )

    if Enum.empty?(errors) do
      updated_files = Enum.reverse(ok_files)

      if Enum.empty?(updated_files) do
        {final_processed_map, results}
      else
        # Get target repositories (excluding the template repository)
        target_repos =
          updated_files
          |> Enum.flat_map(& &1.repositories)
          |> Enum.reject(&(&1.id == template_repo.id))
          |> Enum.uniq_by(& &1.id)

        # Sync to each target repository
        sync_results =
          Enum.map(target_repos, fn repo ->
            sync_to_target_repository(
              repo,
              updated_files,
              template_repo,
              commit,
              github_api,
              sync_backend
            )
          end)

        {final_processed_map, [{:ok, sync_results} | results]}
      end
    else
      error_result =
        {:error,
         "Skipped sync for commit #{commit.id} due to file fetch errors: #{inspect(errors)}"}

      {final_processed_map, [error_result | results]}
    end
  end

  defp fetch_source_file_contents(
         source_files,
         template_repo,
         template_client,
         commit,
         processed_files,
         source_file_to_repo_path
       ) do
    Enum.reduce(source_files, {processed_files, [], []}, fn source_file,
                                                            {processed_map_acc, ok_acc, err_acc} ->
      if Map.has_key?(processed_map_acc, source_file.id) do
        {processed_map_acc, [Map.get(processed_map_acc, source_file.id) | ok_acc], err_acc}
      else
        repo_path = Map.get(source_file_to_repo_path, source_file.id, source_file.target_path)

        case fetch_and_update_source_file(
               source_file,
               template_repo,
               template_client,
               commit.id,
               repo_path
             ) do
          {:ok, updated_source_file} ->
            {Map.put(processed_map_acc, updated_source_file.id, updated_source_file),
             [updated_source_file | ok_acc], err_acc}

          {:error, reason} ->
            {processed_map_acc, ok_acc, [{source_file.id, reason} | err_acc]}
        end
      end
    end)
  end

  defp fetch_and_update_source_file(
         source_file,
         template_repo,
         template_client,
         commit_sha,
         repo_path
       ) do
    github_api = Application.get_env(:repobot, :github_api)

    case github_api.get_file_content(
           template_client,
           template_repo.owner,
           template_repo.name,
           repo_path,
           commit_sha
         ) do
      {:ok, content, _response} ->
        SourceFiles.update_source_file(source_file, %{content: content})

      {:error, reason} ->
        {:error, "Failed to get file content: #{inspect(reason)}"}
    end
  end

  defp sync_to_target_repository(repo, files, template_repo, commit, github_api, sync_backend) do
    # Get files for this repository
    files_for_repo =
      files
      |> Enum.filter(fn source_file ->
        Enum.any?(source_file.repositories, fn r -> r.id == repo.id end)
      end)
      |> Enum.uniq_by(& &1.id)

    # Create a client for the target repository
    target_client = github_api.client(repo.owner, repo.name)

    # Sync the files
    result =
      sync_backend.sync_changes(files_for_repo, template_repo, repo, target_client,
        commit_message: commit.message
      )

    # Log the sync event
    log_sync_event(result, template_repo, repo, commit, files_for_repo)

    result
  end

  defp log_sync_event(result, template_repo, repo, commit, files) do
    sync_event_payload = %{
      template_repository_id: template_repo.id,
      target_repository_id: repo.id,
      commit_sha: commit.id,
      file_ids: Enum.map(files, & &1.id),
      result: elem(result, 0)
    }

    sync_status =
      case elem(result, 0) do
        :ok -> "success"
        :error -> "failed"
      end

    Events.log_repobot_event(
      "sync",
      sync_event_payload,
      repo.organization_id,
      nil,
      repo.id,
      sync_status
    )
  end

  # Refreshes repository files when a push event occurs to the default branch.
  # This ensures our internal repository file data stays synchronized with GitHub.
  defp refresh_repository_files(repository_id, commits) do
    repository = Repositories.get_repository!(repository_id)

    # Extract all file changes from commits
    changed_files = extract_changed_files_from_commits(commits)

    Logger.info(
      "Refreshing #{length(changed_files)} changed files for #{repository.full_name} after push",
      repository_id: repository_id,
      commits_count: length(commits),
      changed_files_count: length(changed_files)
    )

    # Get a user from the organization to use for GitHub API calls
    user = get_organization_user(repository.organization_id)

    case sync_changed_files(repository, changed_files, user) do
      {:ok, results} ->
        Logger.info(
          "Successfully processed #{length(results)} file changes for #{repository.full_name}",
          repository_id: repository_id,
          processed_files: length(results)
        )

        # Log the repository refresh event
        Events.log_repobot_event(
          "repository_refresh",
          %{
            repository_id: repository_id,
            changed_files_count: length(changed_files),
            processed_files_count: length(results),
            trigger: "push_webhook"
          },
          repository.organization_id,
          user && user.id,
          repository_id,
          "success"
        )

        # Broadcast repository files updated event to notify LiveView components
        Phoenix.PubSub.broadcast(
          Repobot.PubSub,
          "repository_files",
          {:repository_files_updated, repository_id,
           %{
             changed_files_count: length(changed_files),
             processed_files_count: length(results),
             trigger: "push_webhook"
           }}
        )

      {:error, reason} ->
        Logger.error("Failed to refresh repository files for #{repository.full_name}",
          repository_id: repository_id,
          error: inspect(reason)
        )

        # Log the failure event
        Events.log_repobot_event(
          "repository_refresh",
          %{
            repository_id: repository_id,
            error: inspect(reason),
            trigger: "push_webhook"
          },
          repository.organization_id,
          user && user.id,
          repository_id,
          "failed"
        )
    end
  end

  # Extracts all file changes from push commits
  defp extract_changed_files_from_commits(commits) do
    commits
    |> Enum.flat_map(fn commit ->
      # Handle both the raw webhook format and the Push struct format
      added = Map.get(commit, :added_files, Map.get(commit, "added", []))
      modified = Map.get(commit, :modified_files, Map.get(commit, "modified", []))
      removed = Map.get(commit, "removed", [])

      # Create file change records with action type
      Enum.concat([
        Enum.map(added, &%{path: &1, action: :added}),
        Enum.map(modified, &%{path: &1, action: :modified}),
        Enum.map(removed, &%{path: &1, action: :removed})
      ])
    end)
    # Remove duplicates if same file changed in multiple commits
    |> Enum.uniq_by(& &1.path)
  end

  # Syncs only the changed files instead of doing a full repository refresh
  defp sync_changed_files(repository, changed_files, user) do
    github_api = Application.get_env(:repobot, :github_api, Repobot.GitHub)

    # Only create client if we have files that need GitHub API calls
    files_needing_api = Enum.filter(changed_files, &(&1.action in [:added, :modified]))

    client = if Enum.empty?(files_needing_api), do: nil, else: github_api.client(user)

    # Check for potential file renames during template conversion
    # This happens when a file is removed and a .liquid version is added in the same commit
    potential_renames = detect_template_renames(changed_files)

    results =
      Enum.map(changed_files, fn %{path: path, action: action} ->
        case action do
          :removed ->
            # Check if this removal is part of a template conversion rename
            case Map.get(potential_renames, path) do
              nil ->
                # Regular file deletion
                delete_repository_file(repository, path)

              new_path ->
                # This is a template conversion rename - update source files instead of deleting
                handle_template_conversion_rename(repository, path, new_path)
            end

          action when action in [:added, :modified] ->
            # Fetch file content from GitHub and update/create in database
            case github_api.get_file_content(client, repository.owner, repository.name, path) do
              {:ok, content, response} ->
                file_data = %{
                  repository_id: repository.id,
                  path: path,
                  name: Path.basename(path),
                  type: "file",
                  size: Map.get(response, "size", 0),
                  sha: Map.get(response, "sha"),
                  content: content,
                  content_updated_at: DateTime.utc_now() |> DateTime.truncate(:second)
                }

                case RepositoryFiles.get_repository_file_by_path(repository.id, path) do
                  nil ->
                    # Create new file
                    case RepositoryFiles.create_repository_file(file_data) do
                      {:ok, file} ->
                        Logger.debug("Created new file #{path}")

                        {:ok, {:created, file}}

                      {:error, reason} ->
                        Logger.error("Failed to create file #{path}: #{inspect(reason)}")
                        {:error, reason}
                    end

                  existing_file ->
                    # Update existing file
                    case RepositoryFiles.update_repository_file(existing_file, file_data) do
                      {:ok, file} ->
                        Logger.debug("Updated file #{path}")

                        {:ok, {:updated, file}}

                      {:error, reason} ->
                        Logger.error("Failed to update file #{path}: #{inspect(reason)}")
                        {:error, reason}
                    end
                end

              {:error, reason} ->
                Logger.error("Failed to fetch content for file #{path}: #{inspect(reason)}")
                {:error, reason}
            end
        end
      end)

    # Check if all operations were successful
    errors = Enum.filter(results, &match?({:error, _}, &1))

    if Enum.empty?(errors) do
      successful_results = Enum.map(results, &elem(&1, 1))
      {:ok, successful_results}
    else
      {:error, "Some file operations failed: #{inspect(errors)}"}
    end
  end

  # Helper function to get a user from the organization for GitHub API calls
  defp get_organization_user(organization_id) do
    import Ecto.Query

    from(u in Repobot.Accounts.User,
      join: uo in Repobot.Accounts.UserOrganization,
      on: u.id == uo.user_id,
      where: uo.organization_id == ^organization_id,
      limit: 1
    )
    |> Repobot.Repo.one()
  end

  # Detects potential template conversion renames by looking for files that are removed
  # and have a corresponding .liquid version added in the same commit
  def detect_template_renames(changed_files) do
    removed_files =
      changed_files
      |> Enum.filter(&(&1.action == :removed))
      |> Enum.map(& &1.path)
      |> MapSet.new()

    added_files =
      changed_files
      |> Enum.filter(&(&1.action == :added))
      |> Enum.map(& &1.path)
      |> MapSet.new()

    # Look for patterns where a file was removed and a .liquid version was added
    removed_files
    |> Enum.reduce(%{}, fn removed_path, acc ->
      liquid_path = removed_path <> ".liquid"

      if MapSet.member?(added_files, liquid_path) do
        Map.put(acc, removed_path, liquid_path)
      else
        acc
      end
    end)
  end

  # Handles regular file deletion
  defp delete_repository_file(repository, path) do
    case RepositoryFiles.get_repository_file_by_path(repository.id, path) do
      nil ->
        Logger.debug("File #{path} not found in database, skipping removal")
        {:ok, :not_found}

      file ->
        case RepositoryFiles.delete_repository_file(file) do
          {:ok, _} ->
            Logger.debug("Deleted file #{path} from database")
            {:ok, :deleted}

          {:error, reason} ->
            Logger.error("Failed to delete file #{path}: #{inspect(reason)}")
            {:error, reason}
        end
    end
  end

  # Handles template conversion renames by updating source files to use the new name
  def handle_template_conversion_rename(repository, old_path, new_path) do
    Logger.info("Detected template conversion rename: #{old_path} -> #{new_path}")

    # Find source files that were imported from this repository and match the old path
    # We need to check for both scenarios:
    # 1. Source files that still have the original name (before conversion)
    # 2. Source files that were already converted and have the .liquid name but target_path matches old_path
    source_files =
      SourceFiles.list_source_files_created_from_repository(repository)
      |> Enum.filter(fn source_file ->
        # Case 1: Source file name matches the old path (not yet converted)
        # Case 2: Source file was already converted (name has .liquid) but target_path matches old_path
        source_file.name == old_path ||
          (source_file.name == new_path && source_file.target_path == old_path)
      end)

    case source_files do
      [] ->
        Logger.debug("No source files found for #{old_path}, proceeding with regular deletion")
        delete_repository_file(repository, old_path)

      files ->
        Logger.info("Found #{length(files)} source files related to #{old_path}")

        # Check if any files need updating (those that don't already have the correct name/template status)
        files_needing_update =
          Enum.filter(files, fn source_file ->
            source_file.name != new_path || !source_file.is_template
          end)

        case files_needing_update do
          [] ->
            Logger.info("All source files already have correct name and template status")
            # Just delete the old repository file since source files are already correct
            delete_repository_file(repository, old_path)

          files_to_update ->
            Logger.info(
              "Updating #{length(files_to_update)} source files to use new name: #{new_path}"
            )

            # Update each source file to use the new name and mark as template
            update_results =
              Enum.map(files_to_update, fn source_file ->
                # Extract the original target path (without .liquid extension)
                target_path = String.replace_suffix(new_path, ".liquid", "")

                case SourceFiles.update_source_file(source_file, %{
                       name: new_path,
                       target_path: target_path,
                       is_template: true
                     }) do
                  {:ok, updated_file} ->
                    Logger.debug("Updated source file #{source_file.id} to use name #{new_path}")
                    {:ok, updated_file}

                  {:error, reason} ->
                    Logger.error(
                      "Failed to update source file #{source_file.id}: #{inspect(reason)}"
                    )

                    {:error, reason}
                end
              end)

            # Check if all updates were successful
            errors = Enum.filter(update_results, &match?({:error, _}, &1))

            if Enum.empty?(errors) do
              # All source files updated successfully, now delete the old repository file
              delete_repository_file(repository, old_path)
            else
              Logger.error("Some source file updates failed: #{inspect(errors)}")
              {:error, "Failed to update some source files during template conversion"}
            end
        end
    end
  end
end
